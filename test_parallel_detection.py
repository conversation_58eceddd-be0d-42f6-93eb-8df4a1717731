import cv2
import numpy as np
import matplotlib.pyplot as plt
from parallel_line_detector import ParallelLineDetector
import os

def create_test_image():
    """
    创建一个包含平行线的测试图像
    """
    # 创建一个白色背景的图像
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 绘制第一组平行线（水平方向）
    y_positions = [100, 150, 200]
    for y in y_positions:
        cv2.line(image, (50, y), (750, y), (0, 0, 0), 3)
    
    # 绘制第二组平行线（倾斜方向）
    start_points = [(100, 300), (150, 350), (200, 400)]
    end_points = [(600, 400), (650, 450), (700, 500)]
    for start, end in zip(start_points, end_points):
        cv2.line(image, start, end, (0, 0, 0), 3)
    
    # 绘制第三组平行线（垂直方向）
    x_positions = [300, 350, 400]
    for x in x_positions:
        cv2.line(image, (x, 50), (x, 250), (0, 0, 0), 3)
    
    # 添加一些噪声线（非平行）
    cv2.line(image, (500, 100), (600, 300), (128, 128, 128), 2)
    cv2.line(image, (50, 500), (200, 550), (128, 128, 128), 2)
    
    return image

def test_with_custom_image():
    """
    使用自定义测试图像进行测试
    """
    print("=== 创建测试图像 ===")
    test_image = create_test_image()
    test_image_path = "test_image.jpg"
    cv2.imwrite(test_image_path, test_image)
    print(f"测试图像已保存到: {test_image_path}")
    
    # 创建检测器
    detector = ParallelLineDetector(angle_threshold=5.0, distance_threshold=40)
    
    print("\n=== 开始平行线检测 ===")
    try:
        parallel_groups, original_image, edges = detector.detect_parallel_lines(test_image_path)
        
        print(f"\n=== 检测结果 ===")
        print(f"总共检测到 {len(parallel_groups)} 组平行线")
        
        for i, group in enumerate(parallel_groups):
            print(f"第 {i+1} 组: {len(group)} 条平行线")
            # 显示每组的角度信息
            if group:
                angle = detector.calculate_angle(group[0][2])
                print(f"  角度: {angle:.1f}°")
        
        # 可视化结果
        if parallel_groups:
            all_lines = []
            for group in parallel_groups:
                for line_data in group:
                    all_lines.append((line_data[1], line_data[2]))
            
            result_image = detector.visualize_results(
                original_image, parallel_groups, all_lines, 
                save_path="test_result.jpg"
            )
            
            print(f"\n结果图像已保存到: test_result.jpg")
            
            # 使用matplotlib显示结果
            plt.figure(figsize=(15, 5))
            
            plt.subplot(1, 3, 1)
            plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
            plt.title("原始图像")
            plt.axis('off')
            
            plt.subplot(1, 3, 2)
            plt.imshow(edges, cmap='gray')
            plt.title("边缘检测结果")
            plt.axis('off')
            
            plt.subplot(1, 3, 3)
            plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
            plt.title(f"平行线检测结果 ({len(parallel_groups)} 组)")
            plt.axis('off')
            
            plt.tight_layout()
            plt.savefig("detection_process.png", dpi=150, bbox_inches='tight')
            plt.show()
            
        else:
            print("未检测到平行线组")
            
    except Exception as e:
        print(f"检测过程中出现错误: {e}")

def test_with_user_image(image_path):
    """
    使用用户提供的图像进行测试
    """
    if not os.path.exists(image_path):
        print(f"错误: 图像文件 '{image_path}' 不存在")
        return
    
    print(f"=== 分析图像: {image_path} ===")
    
    # 创建检测器
    detector = ParallelLineDetector(angle_threshold=3.0, distance_threshold=50)
    
    try:
        parallel_groups, original_image, edges = detector.detect_parallel_lines(image_path)
        
        print(f"\n=== 检测结果 ===")
        print(f"总共检测到 {len(parallel_groups)} 组平行线")
        
        for i, group in enumerate(parallel_groups):
            print(f"第 {i+1} 组: {len(group)} 条平行线")
            if group:
                angle = detector.calculate_angle(group[0][2])
                print(f"  角度: {angle:.1f}°")
        
        # 保存结果
        if parallel_groups:
            all_lines = []
            for group in parallel_groups:
                for line_data in group:
                    all_lines.append((line_data[1], line_data[2]))
            
            result_image = detector.visualize_results(
                original_image, parallel_groups, all_lines, 
                save_path="user_image_result.jpg"
            )
            
            print(f"\n结果图像已保存到: user_image_result.jpg")
        
    except Exception as e:
        print(f"检测过程中出现错误: {e}")

def main():
    """
    主测试函数
    """
    print("平行线检测算法测试")
    print("=" * 50)
    
    # 首先使用测试图像
    test_with_custom_image()
    
    print("\n" + "=" * 50)
    print("如果你有自己的图像文件，可以修改下面的路径进行测试:")
    print("test_with_user_image('your_image_path.jpg')")
    
    # 如果有用户图像，取消注释下面的行
    # test_with_user_image("your_image.jpg")

if __name__ == "__main__":
    main()

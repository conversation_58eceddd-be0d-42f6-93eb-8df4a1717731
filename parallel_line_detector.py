import cv2
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import math
from typing import List, Tu<PERSON>, Dict

class ParallelLineDetector:
    def __init__(self, angle_threshold=5.0, distance_threshold=50):
        """
        初始化平行线检测器
        
        Args:
            angle_threshold: 角度阈值（度），用于判断两条线是否平行
            distance_threshold: 距离阈值，用于判断平行线是否属于同一组
        """
        self.angle_threshold = angle_threshold
        self.distance_threshold = distance_threshold
    
    def preprocess_image(self, image):
        """
        图像预处理：转换为灰度图并进行边缘检测
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Canny边缘检测
        edges = cv2.Canny(blurred, 50, 150, apertureSize=3)
        
        return edges
    
    def detect_lines(self, edges):
        """
        使用霍夫变换检测直线
        """
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is None:
            return []
        
        detected_lines = []
        for line in lines:
            rho, theta = line[0]
            detected_lines.append((rho, theta))
        
        return detected_lines
    
    def line_to_cartesian(self, rho, theta):
        """
        将极坐标形式的直线转换为笛卡尔坐标系中的两个点
        """
        a = np.cos(theta)
        b = np.sin(theta)
        x0 = a * rho
        y0 = b * rho
        
        # 计算直线上的两个点
        x1 = int(x0 + 1000 * (-b))
        y1 = int(y0 + 1000 * (a))
        x2 = int(x0 - 1000 * (-b))
        y2 = int(y0 - 1000 * (a))
        
        return (x1, y1), (x2, y2)
    
    def calculate_angle(self, theta):
        """
        计算直线的角度（度）
        """
        angle = theta * 180 / np.pi
        # 将角度标准化到0-180度范围
        if angle > 90:
            angle = 180 - angle
        return angle
    
    def are_parallel(self, theta1, theta2):
        """
        判断两条直线是否平行
        """
        angle1 = self.calculate_angle(theta1)
        angle2 = self.calculate_angle(theta2)
        
        angle_diff = abs(angle1 - angle2)
        # 考虑角度的周期性
        angle_diff = min(angle_diff, 180 - angle_diff)
        
        return angle_diff <= self.angle_threshold
    
    def calculate_distance_between_parallel_lines(self, rho1, theta1, rho2, theta2):
        """
        计算两条平行线之间的距离
        """
        # 对于平行线，距离就是rho值的差的绝对值
        return abs(rho1 - rho2)
    
    def group_parallel_lines(self, lines):
        """
        将检测到的直线按平行关系分组
        """
        if not lines:
            return []
        
        # 按角度对直线进行初步分组
        angle_groups = defaultdict(list)
        
        for i, (rho, theta) in enumerate(lines):
            angle = self.calculate_angle(theta)
            # 将相似角度的直线分到同一组
            angle_key = round(angle / self.angle_threshold) * self.angle_threshold
            angle_groups[angle_key].append((i, rho, theta))
        
        parallel_groups = []
        
        # 在每个角度组内进一步细分平行线组
        for angle_key, group_lines in angle_groups.items():
            if len(group_lines) < 2:
                continue
            
            # 按rho值排序
            group_lines.sort(key=lambda x: x[1])
            
            current_group = [group_lines[0]]
            
            for i in range(1, len(group_lines)):
                prev_line = current_group[-1]
                curr_line = group_lines[i]
                
                # 检查是否应该加入当前组
                distance = abs(curr_line[1] - prev_line[1])
                
                if distance <= self.distance_threshold:
                    current_group.append(curr_line)
                else:
                    # 如果当前组有至少2条线，则保存
                    if len(current_group) >= 2:
                        parallel_groups.append(current_group)
                    current_group = [curr_line]
            
            # 处理最后一组
            if len(current_group) >= 2:
                parallel_groups.append(current_group)
        
        return parallel_groups
    
    def detect_parallel_lines(self, image_path):
        """
        主函数：检测图片中的平行线组
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 预处理
        edges = self.preprocess_image(image)
        
        # 检测直线
        lines = self.detect_lines(edges)
        
        if not lines:
            print("未检测到任何直线")
            return [], image, edges
        
        print(f"检测到 {len(lines)} 条直线")
        
        # 分组平行线
        parallel_groups = self.group_parallel_lines(lines)
        
        print(f"检测到 {len(parallel_groups)} 组平行线")
        
        return parallel_groups, image, edges
    
    def visualize_results(self, image, parallel_groups, lines, save_path=None):
        """
        可视化检测结果
        """
        result_image = image.copy()
        
        # 为每组平行线分配不同的颜色
        colors = [
            (255, 0, 0),    # 红色
            (0, 255, 0),    # 绿色
            (0, 0, 255),    # 蓝色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 品红色
            (0, 255, 255),  # 黄色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]
        
        for group_idx, group in enumerate(parallel_groups):
            color = colors[group_idx % len(colors)]
            
            for line_idx, rho, theta in group:
                # 转换为笛卡尔坐标
                (x1, y1), (x2, y2) = self.line_to_cartesian(rho, theta)
                
                # 裁剪到图像边界
                height, width = image.shape[:2]
                x1 = max(0, min(width-1, x1))
                y1 = max(0, min(height-1, y1))
                x2 = max(0, min(width-1, x2))
                y2 = max(0, min(height-1, y2))
                
                cv2.line(result_image, (x1, y1), (x2, y2), color, 2)
        
        if save_path:
            cv2.imwrite(save_path, result_image)
        
        return result_image

def main():
    """
    主函数示例
    """
    # 创建检测器实例
    detector = ParallelLineDetector(angle_threshold=3.0, distance_threshold=30)
    
    # 检测平行线（需要提供图片路径）
    image_path = "test_image.jpg"  # 请替换为实际的图片路径
    
    try:
        parallel_groups, original_image, edges = detector.detect_parallel_lines(image_path)
        
        # 输出结果
        print(f"\n=== 平行线检测结果 ===")
        print(f"总共检测到 {len(parallel_groups)} 组平行线")
        
        for i, group in enumerate(parallel_groups):
            print(f"第 {i+1} 组: {len(group)} 条平行线")
        
        # 可视化结果
        if parallel_groups:
            # 获取所有检测到的直线
            all_lines = []
            for group in parallel_groups:
                for line_data in group:
                    all_lines.append((line_data[1], line_data[2]))  # (rho, theta)
            
            result_image = detector.visualize_results(
                original_image, parallel_groups, all_lines, 
                save_path="parallel_lines_result.jpg"
            )
            
            print("结果已保存到 'parallel_lines_result.jpg'")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()

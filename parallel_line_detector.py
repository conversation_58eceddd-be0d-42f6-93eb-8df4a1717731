import cv2
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import math
from typing import List, Tu<PERSON>, Dict

class ParallelLineDetector:
    def __init__(self, angle_threshold=3.0, distance_threshold=30, min_line_length=50, max_line_gap=10):
        """
        初始化平行线检测器

        Args:
            angle_threshold: 角度阈值（度），用于判断两条线是否平行
            distance_threshold: 距离阈值，用于判断平行线是否属于同一组
            min_line_length: 最小线段长度
            max_line_gap: 线段间最大间隙
        """
        self.angle_threshold = angle_threshold
        self.distance_threshold = distance_threshold
        self.min_line_length = min_line_length
        self.max_line_gap = max_line_gap
    
    def preprocess_image(self, image):
        """
        图像预处理：转换为灰度图并进行边缘检测
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # 自适应直方图均衡化增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(enhanced, (3, 3), 0)

        # 自适应Canny边缘检测
        median_val = np.median(blurred)
        lower = int(max(0, 0.7 * median_val))
        upper = int(min(255, 1.3 * median_val))
        edges = cv2.Canny(blurred, lower, upper, apertureSize=3)

        return edges, gray

    def detect_lines(self, edges):
        """
        使用改进的霍夫变换检测直线
        """
        # 使用概率霍夫变换检测线段
        lines_p = cv2.HoughLinesP(
            edges,
            rho=1,
            theta=np.pi/180,
            threshold=50,
            minLineLength=self.min_line_length,
            maxLineGap=self.max_line_gap
        )

        if lines_p is None:
            return []

        # 将线段转换为直线参数
        detected_lines = []
        for line in lines_p:
            x1, y1, x2, y2 = line[0]

            # 计算线段的角度和距离参数
            if x2 - x1 == 0:  # 垂直线
                theta = np.pi / 2
                rho = x1
            else:
                # 计算斜率和角度
                slope = (y2 - y1) / (x2 - x1)
                theta = np.arctan(-1/slope) if slope != 0 else 0

                # 计算到原点的距离
                a = np.cos(theta)
                b = np.sin(theta)
                rho = abs(a * x1 + b * y1)

                # 确保rho为正值
                if a * x1 + b * y1 < 0:
                    rho = -rho
                    theta = theta + np.pi

            # 标准化角度到[0, π)
            while theta < 0:
                theta += np.pi
            while theta >= np.pi:
                theta -= np.pi

            detected_lines.append((rho, theta, (x1, y1, x2, y2)))

        return detected_lines
    
    def line_to_cartesian(self, rho, theta):
        """
        将极坐标形式的直线转换为笛卡尔坐标系中的两个点
        """
        a = np.cos(theta)
        b = np.sin(theta)
        x0 = a * rho
        y0 = b * rho
        
        # 计算直线上的两个点
        x1 = int(x0 + 1000 * (-b))
        y1 = int(y0 + 1000 * (a))
        x2 = int(x0 - 1000 * (-b))
        y2 = int(y0 - 1000 * (a))
        
        return (x1, y1), (x2, y2)
    
    def calculate_angle(self, theta):
        """
        计算直线的角度（度）
        """
        angle = theta * 180 / np.pi
        # 将角度标准化到0-180度范围
        if angle > 90:
            angle = 180 - angle
        return angle
    
    def are_parallel(self, theta1, theta2):
        """
        判断两条直线是否平行
        """
        angle1 = self.calculate_angle(theta1)
        angle2 = self.calculate_angle(theta2)
        
        angle_diff = abs(angle1 - angle2)
        # 考虑角度的周期性
        angle_diff = min(angle_diff, 180 - angle_diff)
        
        return angle_diff <= self.angle_threshold
    
    def calculate_distance_between_parallel_lines(self, rho1, theta1, rho2, theta2):
        """
        计算两条平行线之间的距离
        """
        # 对于平行线，距离就是rho值的差的绝对值
        return abs(rho1 - rho2)

    def merge_similar_lines(self, lines):
        """
        合并相似的线条以减少重复检测
        """
        if not lines:
            return []

        merged_lines = []
        used = [False] * len(lines)

        for i, (rho1, theta1, coords1) in enumerate(lines):
            if used[i]:
                continue

            similar_lines = [(rho1, theta1, coords1)]
            used[i] = True

            for j, (rho2, theta2, coords2) in enumerate(lines):
                if used[j] or i == j:
                    continue

                # 检查是否为相似线条
                angle_diff = abs(self.calculate_angle(theta1) - self.calculate_angle(theta2))
                angle_diff = min(angle_diff, 180 - angle_diff)
                rho_diff = abs(rho1 - rho2)

                if angle_diff <= 2.0 and rho_diff <= 10:
                    similar_lines.append((rho2, theta2, coords2))
                    used[j] = True

            # 计算平均参数
            avg_rho = np.mean([line[0] for line in similar_lines])
            avg_theta = np.mean([line[1] for line in similar_lines])
            merged_lines.append((avg_rho, avg_theta, coords1))

        return merged_lines

    def group_parallel_lines(self, lines):
        """
        将检测到的直线按平行关系分组
        """
        if not lines:
            return []

        # 首先合并相似的线条
        merged_lines = self.merge_similar_lines(lines)

        # 按角度对直线进行初步分组
        angle_groups = defaultdict(list)

        for i, (rho, theta, coords) in enumerate(merged_lines):
            angle = self.calculate_angle(theta)
            # 使用更精细的角度分组
            angle_key = round(angle / (self.angle_threshold / 2)) * (self.angle_threshold / 2)
            angle_groups[angle_key].append((i, rho, theta, coords))

        parallel_groups = []

        # 在每个角度组内进一步细分平行线组
        for angle_key, group_lines in angle_groups.items():
            if len(group_lines) < 2:
                continue

            # 按rho值排序
            group_lines.sort(key=lambda x: x[1])

            # 使用聚类方法分组
            clusters = []
            for line in group_lines:
                added_to_cluster = False

                for cluster in clusters:
                    # 检查是否可以加入现有聚类
                    avg_rho = np.mean([l[1] for l in cluster])
                    if abs(line[1] - avg_rho) <= self.distance_threshold:
                        cluster.append(line)
                        added_to_cluster = True
                        break

                if not added_to_cluster:
                    clusters.append([line])

            # 只保留包含至少2条线的聚类
            for cluster in clusters:
                if len(cluster) >= 2:
                    parallel_groups.append(cluster)

        return parallel_groups
    
    def detect_parallel_lines(self, image_path):
        """
        主函数：检测图片中的平行线组
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        print(f"图像尺寸: {image.shape}")

        # 预处理
        edges, gray = self.preprocess_image(image)

        # 检测直线
        lines = self.detect_lines(edges)

        if not lines:
            print("未检测到任何直线")
            return [], image, edges, gray, []

        print(f"检测到 {len(lines)} 条线段")

        # 分组平行线
        parallel_groups = self.group_parallel_lines(lines)

        print(f"检测到 {len(parallel_groups)} 组平行线")

        # 输出每组的详细信息
        for i, group in enumerate(parallel_groups):
            angle = self.calculate_angle(group[0][2])
            print(f"  第 {i+1} 组: {len(group)} 条平行线, 角度: {angle:.1f}°")

        return parallel_groups, image, edges, gray, lines
    
    def clip_line_to_image(self, x1, y1, x2, y2, width, height):
        """
        将直线裁剪到图像边界内
        """
        # 使用Liang-Barsky算法裁剪直线
        def clip_test(p, q, u1, u2):
            if p < 0:
                r = q / p
                if r > u2:
                    return False, u1, u2
                elif r > u1:
                    u1 = r
            elif p > 0:
                r = q / p
                if r < u1:
                    return False, u1, u2
                elif r < u2:
                    u2 = r
            elif q < 0:
                return False, u1, u2
            return True, u1, u2

        dx = x2 - x1
        dy = y2 - y1
        u1, u2 = 0.0, 1.0

        # 左边界
        accept, u1, u2 = clip_test(-dx, x1, u1, u2)
        if not accept:
            return None

        # 右边界
        accept, u1, u2 = clip_test(dx, width - 1 - x1, u1, u2)
        if not accept:
            return None

        # 下边界
        accept, u1, u2 = clip_test(-dy, y1, u1, u2)
        if not accept:
            return None

        # 上边界
        accept, u1, u2 = clip_test(dy, height - 1 - y1, u1, u2)
        if not accept:
            return None

        if u2 < 1:
            x2 = x1 + u2 * dx
            y2 = y1 + u2 * dy

        if u1 > 0:
            x1 = x1 + u1 * dx
            y1 = y1 + u1 * dy

        return int(x1), int(y1), int(x2), int(y2)

    def visualize_results(self, image, parallel_groups, all_lines=None, save_path=None, show_all_lines=False):
        """
        可视化检测结果
        """
        result_image = image.copy()
        height, width = image.shape[:2]

        # 为每组平行线分配不同的颜色
        colors = [
            (0, 0, 255),    # 红色 (BGR格式)
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 255, 255),  # 黄色
            (255, 0, 255),  # 品红色
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (0, 165, 255),  # 橙色
            (128, 128, 0),  # 橄榄色
            (128, 0, 0),    # 栗色
        ]

        # 如果需要显示所有检测到的线条（灰色）
        if show_all_lines and all_lines:
            for rho, theta, coords in all_lines:
                if len(coords) == 4:  # 线段坐标
                    x1, y1, x2, y2 = coords
                    clipped = self.clip_line_to_image(x1, y1, x2, y2, width, height)
                    if clipped:
                        cv2.line(result_image, (clipped[0], clipped[1]),
                                (clipped[2], clipped[3]), (128, 128, 128), 1)

        # 绘制平行线组
        for group_idx, group in enumerate(parallel_groups):
            color = colors[group_idx % len(colors)]

            for _, rho, theta, coords in group:
                if len(coords) == 4:  # 使用原始线段坐标
                    x1, y1, x2, y2 = coords
                    clipped = self.clip_line_to_image(x1, y1, x2, y2, width, height)
                    if clipped:
                        cv2.line(result_image, (clipped[0], clipped[1]),
                                (clipped[2], clipped[3]), color, 3)
                else:  # 使用极坐标转换
                    (x1, y1), (x2, y2) = self.line_to_cartesian(rho, theta)
                    clipped = self.clip_line_to_image(x1, y1, x2, y2, width, height)
                    if clipped:
                        cv2.line(result_image, (clipped[0], clipped[1]),
                                (clipped[2], clipped[3]), color, 3)

        # 添加文本标注
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(result_image, f'Parallel Groups: {len(parallel_groups)}',
                   (10, 30), font, 1, (255, 255, 255), 2)

        for i, group in enumerate(parallel_groups):
            angle = self.calculate_angle(group[0][2])
            text = f'Group {i+1}: {len(group)} lines, {angle:.1f}°'
            cv2.putText(result_image, text, (10, 60 + i * 25),
                       font, 0.6, colors[i % len(colors)], 2)

        if save_path:
            cv2.imwrite(save_path, result_image)
            print(f"结果图像已保存到: {save_path}")

        return result_image

def create_test_image():
    """
    创建一个包含平行线的测试图像
    """
    # 创建一个白色背景的图像
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255

    # 绘制第一组平行线（水平方向）
    y_positions = [100, 150, 200]
    for y in y_positions:
        cv2.line(image, (50, y), (750, y), (0, 0, 0), 3)

    # 绘制第二组平行线（倾斜方向）
    start_points = [(100, 300), (150, 350), (200, 400)]
    end_points = [(600, 400), (650, 450), (700, 500)]
    for start, end in zip(start_points, end_points):
        cv2.line(image, start, end, (0, 0, 0), 3)

    # 绘制第三组平行线（垂直方向）
    x_positions = [300, 350, 400]
    for x in x_positions:
        cv2.line(image, (x, 50), (x, 250), (0, 0, 0), 3)

    # 添加一些噪声线（非平行）
    cv2.line(image, (500, 100), (600, 300), (128, 128, 128), 2)
    cv2.line(image, (50, 500), (200, 550), (128, 128, 128), 2)

    return image

def main():
    """
    主函数示例
    """
    print("=" * 60)
    print("🔍 平行线检测算法演示")
    print("=" * 60)

    # 创建测试图像
    print("创建测试图像...")
    test_image = create_test_image()
    test_image_path = "test_image.jpg"
    cv2.imwrite(test_image_path, test_image)
    print(f"✅ 测试图像已保存: {test_image_path}")

    # 创建检测器实例
    detector = ParallelLineDetector(
        angle_threshold=3.0,
        distance_threshold=40,
        min_line_length=30,
        max_line_gap=10
    )

    try:
        print(f"\n开始检测平行线...")
        parallel_groups, original_image, edges, gray, all_lines = detector.detect_parallel_lines(test_image_path)

        # 输出结果
        print(f"\n=== 平行线检测结果 ===")
        print(f"总共检测到 {len(parallel_groups)} 组平行线")

        total_parallel_lines = sum(len(group) for group in parallel_groups)
        print(f"平行线总数: {total_parallel_lines}")

        # 可视化结果
        result_image = detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path="parallel_lines_result.jpg",
            show_all_lines=True
        )

        # 使用matplotlib显示结果
        plt.figure(figsize=(20, 5))

        plt.subplot(1, 4, 1)
        plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        plt.title("原始图像")
        plt.axis('off')

        plt.subplot(1, 4, 2)
        plt.imshow(gray, cmap='gray')
        plt.title("灰度图像")
        plt.axis('off')

        plt.subplot(1, 4, 3)
        plt.imshow(edges, cmap='gray')
        plt.title("边缘检测")
        plt.axis('off')

        plt.subplot(1, 4, 4)
        plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        plt.title(f"平行线检测结果\n({len(parallel_groups)} 组平行线)")
        plt.axis('off')

        plt.tight_layout()
        plt.savefig("detection_process.png", dpi=150, bbox_inches='tight')
        plt.show()

        print(f"\n✅ 检测完成!")
        print(f"📊 结果图像: parallel_lines_result.jpg")
        print(f"📊 处理过程: detection_process.png")

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
